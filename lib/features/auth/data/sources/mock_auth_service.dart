import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:escooter/core/error/api_exceptions.dart';
import 'package:escooter/features/auth/data/models/login_reponse.dart';
import 'package:escooter/features/auth/data/sources/auth_service.dart';
import 'package:escooter/features/auth/domain/entities/create_user_request.dart';
import 'package:escooter/features/auth/domain/entities/login_request.dart';
import 'package:escooter/utils/logger.dart';
import 'package:injectable/injectable.dart';
import 'package:http/http.dart' as http;

/// Mock Authentication Service for Testing
/// This service provides static demo credentials for testing the app
@injectable
@Named('mockAuthApiService')
class MockAuthApiService {
  
  // Demo user credentials
  static const String DEMO_PHONE = "+966501234567";
  static const String DEMO_OTP = "123456";
  static const String DEMO_VERIFICATION_ID = "mock_verification_123";
  static const String DEMO_TOKEN = "mock_jwt_token_12345";

  // Alternative demo users with proper country codes
  static const Map<String, Map<String, String>> DEMO_USERS = {
    // Saudi Arabia numbers (+966)
    "+966501234567": {
      "otp": "123456",
      "firstName": "Ahmed",
      "lastName": "Al-Rashid",
      "email": "<EMAIL>"
    },
    "+966551234567": {
      "otp": "654321",
      "firstName": "Fatima",
      "lastName": "Al-Zahra",
      "email": "<EMAIL>"
    },
    // US numbers (+1) for testing
    "+1234567890": {
      "otp": "111111",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    // UAE numbers (+971) for testing
    "+971501234567": {
      "otp": "222222",
      "firstName": "Omar",
      "lastName": "Hassan",
      "email": "<EMAIL>"
    },
    // Generic test number
    "+1111111111": {
      "otp": "999999",
      "firstName": "Test",
      "lastName": "User",
      "email": "<EMAIL>"
    }
  };

  Future<Either<ApiException, Map<String, dynamic>>> sendOtp(
      String phoneNumber) async {
    try {
      AppLogger.log('Mock: Sending OTP to $phoneNumber');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Check if phone number is in our demo users
      if (DEMO_USERS.containsKey(phoneNumber)) {
        final responseData = {
          "status": "success",
          "message": "OTP sent successfully",
          "data": {
            "verificationId": DEMO_VERIFICATION_ID,
            "phoneNumber": phoneNumber,
            "expiresIn": 300 // 5 minutes
          }
        };
        
        AppLogger.log('Mock OTP Response: ${jsonEncode(responseData)}');
        return Right(responseData);
      } else {
        return Left(ApiException('Phone number not found in demo users. Use: ${DEMO_USERS.keys.join(", ")}'));
      }
    } catch (e) {
      AppLogger.error('Mock Error sending OTP: $e');
      return Left(ApiException.fromError(e));
    }
  }

  Future<Either<ApiException, Map<String, dynamic>>> verifyOtp({
    required String phoneNumber,
    required String verificationId,
    required String otp,
  }) async {
    try {
      AppLogger.log('Mock: Verifying OTP for $phoneNumber with code $otp');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Check if phone number exists and OTP matches
      if (DEMO_USERS.containsKey(phoneNumber) && 
          DEMO_USERS[phoneNumber]!["otp"] == otp &&
          verificationId == DEMO_VERIFICATION_ID) {
        
        final user = DEMO_USERS[phoneNumber]!;
        final responseData = {
          "status": "success",
          "message": "OTP verified successfully",
          "data": {
            "token": DEMO_TOKEN,
            "refreshToken": "mock_refresh_token_67890",
            "isNewUser": false,
            "user": {
              "id": "mock_user_${phoneNumber.replaceAll('+', '')}",
              "phoneNumber": phoneNumber,
              "firstName": user["firstName"],
              "lastName": user["lastName"],
              "email": user["email"],
              "isVerified": true,
              "createdAt": DateTime.now().toIso8601String(),
            }
          }
        };
        
        AppLogger.log('Mock OTP Verification Success: ${jsonEncode(responseData)}');
        return Right(responseData);
      } else {
        return Left(ApiException('Invalid OTP or verification ID. Use OTP: ${DEMO_USERS[phoneNumber]?["otp"] ?? "Phone not found"}'));
      }
    } catch (e) {
      AppLogger.error('Mock Error verifying OTP: $e');
      return Left(ApiException.fromError(e));
    }
  }

  Future<Either<ApiException, Map<String, dynamic>>> registerUser(
      CreateUserRequest createUserRequest) async {
    try {
      AppLogger.log('Mock: Registering user ${createUserRequest.phoneNumber}');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      final responseData = {
        "status": "success",
        "message": "User registered successfully",
        "data": {
          "user": {
            "id": "mock_user_${createUserRequest.phoneNumber.replaceAll('+', '')}",
            "phoneNumber": createUserRequest.phoneNumber,
            "firstName": createUserRequest.firstName,
            "lastName": createUserRequest.lastName,
            "email": createUserRequest.email,
            "isVerified": true,
            "createdAt": DateTime.now().toIso8601String(),
          }
        }
      };
      
      AppLogger.log('Mock Registration Success: ${jsonEncode(responseData)}');
      return Right(responseData);
    } catch (e) {
      AppLogger.error('Mock Error registering user: $e');
      return Left(ApiException.fromError(e));
    }
  }

  Future<LoginResponse> login(LoginRequest request) async {
    try {
      AppLogger.log('Mock: Login attempt for ${request.phoneNumber}');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      if (DEMO_USERS.containsKey(request.phoneNumber)) {
        final loginResponse = LoginResponse(
          token: DEMO_TOKEN,
          refreshToken: "mock_refresh_token_67890",
          isNewUser: false,
        );
        
        AppLogger.log('Mock Login Success');
        return loginResponse;
      } else {
        throw ApiException('Invalid phone number for demo. Use: ${DEMO_USERS.keys.join(", ")}');
      }
    } catch (e) {
      throw ApiException('Mock login failed: $e');
    }
  }

  Future<Map<String, dynamic>> getUserProfile(String token) async {
    try {
      AppLogger.log('Mock: Getting user profile with token $token');

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      if (token == DEMO_TOKEN) {
        // Use the first demo user as default profile
        final defaultUser = DEMO_USERS[DEMO_PHONE] ?? DEMO_USERS.values.first;

        final userData = {
          "id": "mock_user_${DEMO_PHONE.replaceAll('+', '')}",
          "phoneNumber": DEMO_PHONE,
          "firstName": defaultUser["firstName"],
          "lastName": defaultUser["lastName"],
          "email": defaultUser["email"],
          "isVerified": true,
          "dateOfBirth": "1990-01-01T00:00:00.000Z",
          "gender": "male",
          "location": "Saudi Arabia",
          "walletBalance": 25.50,
          "createdAt": DateTime.now().toIso8601String(),
        };

        // Wrap in the expected response format
        final profileData = {
          "status": "success",
          "message": "Profile retrieved successfully",
          "data": userData
        };

        AppLogger.log('Mock Profile Success: ${jsonEncode(profileData)}');
        return profileData;
      } else {
        throw ApiException('Invalid token for demo');
      }
    } catch (e) {
      AppLogger.error('Mock Error getting profile: $e');
      throw ApiException('Mock failed to get user profile: $e');
    }
  }

  Future<Either<ApiException, Map<String, dynamic>>> loginWithPhone(
      String phoneNumber) async {
    try {
      AppLogger.log('Mock: Initiating login with phone number: $phoneNumber');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      if (DEMO_USERS.containsKey(phoneNumber)) {
        final responseData = {
          "status": "success",
          "message": "OTP sent for login",
          "data": {
            "verificationId": DEMO_VERIFICATION_ID,
            "phoneNumber": phoneNumber,
            "expiresIn": 300
          }
        };
        
        AppLogger.log('Mock Login with Phone Success: ${jsonEncode(responseData)}');
        return Right(responseData);
      } else {
        return Left(ApiException('Phone number not found in demo users. Use: ${DEMO_USERS.keys.join(", ")}'));
      }
    } catch (e) {
      AppLogger.error('Mock Error in login with phone: $e');
      return Left(ApiException('Mock failed to login: $e'));
    }
  }
}

/// Wrapper class to make MockAuthApiService compatible with AuthApiService
class MockAuthServiceWrapper extends AuthApiService {
  final MockAuthApiService _mockService;

  MockAuthServiceWrapper(this._mockService) : super(http.Client());

  @override
  Future<Either<ApiException, Map<String, dynamic>>> sendOtp(String phoneNumber) {
    return _mockService.sendOtp(phoneNumber);
  }

  @override
  Future<Either<ApiException, Map<String, dynamic>>> verifyOtp({
    required String phoneNumber,
    required String verificationId,
    required String otp,
  }) {
    return _mockService.verifyOtp(
      phoneNumber: phoneNumber,
      verificationId: verificationId,
      otp: otp,
    );
  }

  @override
  Future<Either<ApiException, Map<String, dynamic>>> registerUser(
      CreateUserRequest createUserRequest) {
    return _mockService.registerUser(createUserRequest);
  }

  @override
  Future<LoginResponse> login(LoginRequest request) {
    return _mockService.login(request);
  }

  @override
  Future<Map<String, dynamic>> getUserProfile(String token) {
    return _mockService.getUserProfile(token);
  }

  @override
  Future<Either<ApiException, Map<String, dynamic>>> loginWithPhone(String phoneNumber) {
    return _mockService.loginWithPhone(phoneNumber);
  }
}
